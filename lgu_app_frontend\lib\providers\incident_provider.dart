import 'dart:io';
import 'package:flutter/foundation.dart';
import '../models/incident.dart';
import '../models/incident_category.dart';
import '../services/incident_service.dart';

class IncidentProvider with ChangeNotifier {
  final IncidentService _incidentService = IncidentService();

  // State variables
  List<Incident> _incidents = [];
  List<IncidentCategory> _categories = [];
  Incident? _selectedIncident;
  bool _isLoading = false;
  String? _error;
  
  // Pagination
  int _currentPage = 1;
  int _lastPage = 1;
  int _total = 0;
  bool _hasMoreData = true;

  // Filters
  String? _statusFilter;
  int? _categoryFilter;
  String? _priorityFilter;
  bool? _emergencyFilter;
  bool _showMyReports = false;

  // Getters
  List<Incident> get incidents => _incidents;
  List<IncidentCategory> get categories => _categories;
  Incident? get selectedIncident => _selectedIncident;
  bool get isLoading => _isLoading;
  String? get error => _error;
  int get currentPage => _currentPage;
  int get total => _total;
  bool get hasMoreData => _hasMoreData;
  String? get statusFilter => _statusFilter;
  int? get categoryFilter => _categoryFilter;
  String? get priorityFilter => _priorityFilter;
  bool? get emergencyFilter => _emergencyFilter;
  bool get showMyReports => _showMyReports;

  // Load categories
  Future<void> loadCategories({bool? emergency}) async {
    try {
      _setLoading(true);
      _categories = await _incidentService.getCategories(emergency: emergency);
      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load incidents
  Future<void> loadIncidents({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 1;
      _incidents.clear();
      _hasMoreData = true;
    }

    if (!_hasMoreData && !refresh) return;

    try {
      _setLoading(true);
      
      final result = await _incidentService.getIncidents(
        page: _currentPage,
        status: _statusFilter,
        categoryId: _categoryFilter,
        priority: _priorityFilter,
        emergency: _emergencyFilter,
        myReports: _showMyReports,
      );

      final List<Incident> newIncidents = result['incidents'];
      final pagination = result['pagination'];

      if (refresh) {
        _incidents = newIncidents;
      } else {
        _incidents.addAll(newIncidents);
      }

      _currentPage = pagination['current_page'];
      _lastPage = pagination['last_page'];
      _total = pagination['total'];
      _hasMoreData = _currentPage < _lastPage;

      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Load more incidents (pagination)
  Future<void> loadMoreIncidents() async {
    if (_hasMoreData && !_isLoading) {
      _currentPage++;
      await loadIncidents();
    }
  }

  // Load single incident
  Future<void> loadIncident(int id) async {
    try {
      _setLoading(true);
      _selectedIncident = await _incidentService.getIncident(id);
      _clearError();
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Submit incident
  Future<Incident?> submitIncident({
    required int categoryId,
    required String title,
    required String description,
    required double latitude,
    required double longitude,
    String? address,
    String? landmark,
    bool isAnonymous = false,
    String? reporterName,
    String? reporterPhone,
    String? reporterEmail,
    List<File>? mediaFiles,
  }) async {
    try {
      _setLoading(true);
      
      final incident = await _incidentService.submitIncident(
        categoryId: categoryId,
        title: title,
        description: description,
        latitude: latitude,
        longitude: longitude,
        address: address,
        landmark: landmark,
        isAnonymous: isAnonymous,
        reporterName: reporterName,
        reporterPhone: reporterPhone,
        reporterEmail: reporterEmail,
        mediaFiles: mediaFiles,
      );

      // Add to the beginning of the list
      _incidents.insert(0, incident);
      _total++;
      
      _clearError();
      return incident;
    } catch (e) {
      _setError(e.toString());
      return null;
    } finally {
      _setLoading(false);
    }
  }

  // Update incident status (for staff)
  Future<bool> updateIncidentStatus({
    required int incidentId,
    required String status,
    String? message,
    int? assignedDepartmentId,
    int? assignedToUserId,
  }) async {
    try {
      _setLoading(true);
      
      final updatedIncident = await _incidentService.updateIncidentStatus(
        incidentId: incidentId,
        status: status,
        message: message,
        assignedDepartmentId: assignedDepartmentId,
        assignedToUserId: assignedToUserId,
      );

      // Update in the list
      final index = _incidents.indexWhere((incident) => incident.id == incidentId);
      if (index != -1) {
        _incidents[index] = updatedIncident;
      }

      // Update selected incident if it's the same
      if (_selectedIncident?.id == incidentId) {
        _selectedIncident = updatedIncident;
      }

      _clearError();
      return true;
    } catch (e) {
      _setError(e.toString());
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Apply filters
  void applyFilters({
    String? status,
    int? category,
    String? priority,
    bool? emergency,
    bool? myReports,
  }) {
    _statusFilter = status;
    _categoryFilter = category;
    _priorityFilter = priority;
    _emergencyFilter = emergency;
    _showMyReports = myReports ?? false;
    
    loadIncidents(refresh: true);
  }

  // Clear filters
  void clearFilters() {
    _statusFilter = null;
    _categoryFilter = null;
    _priorityFilter = null;
    _emergencyFilter = null;
    _showMyReports = false;
    
    loadIncidents(refresh: true);
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearSelectedIncident() {
    _selectedIncident = null;
    notifyListeners();
  }
}
