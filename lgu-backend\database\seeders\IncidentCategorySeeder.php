<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\IncidentCategory;

class IncidentCategorySeeder extends Seeder
{
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Traffic Incident',
                'slug' => 'traffic-incident',
                'description' => 'Road accidents, traffic violations, roadblocks',
                'icon' => 'traffic',
                'color' => '#ff6b6b',
                'priority_level' => 2,
                'is_emergency' => false,
            ],
            [
                'name' => 'Medical Emergency',
                'slug' => 'medical-emergency',
                'description' => 'Medical emergencies requiring immediate attention',
                'icon' => 'medical',
                'color' => '#ff4757',
                'priority_level' => 4,
                'is_emergency' => true,
            ],
            [
                'name' => 'Fire Emergency',
                'slug' => 'fire-emergency',
                'description' => 'Fire incidents and emergencies',
                'icon' => 'fire',
                'color' => '#ff3838',
                'priority_level' => 4,
                'is_emergency' => true,
            ],
            [
                'name' => 'Crime/Security',
                'slug' => 'crime-security',
                'description' => 'Criminal activities, security concerns',
                'icon' => 'security',
                'color' => '#2f3542',
                'priority_level' => 3,
                'is_emergency' => true,
            ],
            [
                'name' => 'Flooding',
                'slug' => 'flooding',
                'description' => 'Flood incidents and water-related emergencies',
                'icon' => 'water',
                'color' => '#3742fa',
                'priority_level' => 3,
                'is_emergency' => false,
            ],
            [
                'name' => 'Power Outage',
                'slug' => 'power-outage',
                'description' => 'Electrical power interruptions',
                'icon' => 'power',
                'color' => '#ffa502',
                'priority_level' => 2,
                'is_emergency' => false,
            ],
            [
                'name' => 'Water Supply Issue',
                'slug' => 'water-supply',
                'description' => 'Water supply interruptions or contamination',
                'icon' => 'water-drop',
                'color' => '#70a1ff',
                'priority_level' => 2,
                'is_emergency' => false,
            ],
            [
                'name' => 'Road Damage',
                'slug' => 'road-damage',
                'description' => 'Potholes, road damage, infrastructure issues',
                'icon' => 'road',
                'color' => '#747d8c',
                'priority_level' => 1,
                'is_emergency' => false,
            ],
            [
                'name' => 'Waste Management',
                'slug' => 'waste-management',
                'description' => 'Garbage collection, illegal dumping',
                'icon' => 'trash',
                'color' => '#57606f',
                'priority_level' => 1,
                'is_emergency' => false,
            ],
            [
                'name' => 'Public Disturbance',
                'slug' => 'public-disturbance',
                'description' => 'Noise complaints, public gatherings',
                'icon' => 'volume',
                'color' => '#a4b0be',
                'priority_level' => 1,
                'is_emergency' => false,
            ],
            [
                'name' => 'Environmental Hazard',
                'slug' => 'environmental-hazard',
                'description' => 'Environmental concerns, pollution',
                'icon' => 'leaf',
                'color' => '#2ed573',
                'priority_level' => 2,
                'is_emergency' => false,
            ],
            [
                'name' => 'Animal Control',
                'slug' => 'animal-control',
                'description' => 'Stray animals, animal-related incidents',
                'icon' => 'paw',
                'color' => '#ffa726',
                'priority_level' => 1,
                'is_emergency' => false,
            ],
        ];

        foreach ($categories as $category) {
            IncidentCategory::create($category);
        }
    }
}
