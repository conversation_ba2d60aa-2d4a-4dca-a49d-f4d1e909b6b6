<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\IncidentCategory;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Traffic Incident',
                'slug' => 'traffic-incident',
                'description' => 'Traffic accidents, road blockages, traffic light issues',
                'color' => '#FF5722',
                'icon' => 'traffic',
                'is_emergency' => false,
            ],
            [
                'name' => 'Infrastructure',
                'slug' => 'infrastructure',
                'description' => 'Road damage, broken streetlights, public facility issues',
                'color' => '#2196F3',
                'icon' => 'construction',
                'is_emergency' => false,
            ],
            [
                'name' => 'Public Safety',
                'slug' => 'public-safety',
                'description' => 'Crime reports, suspicious activities, safety concerns',
                'color' => '#F44336',
                'icon' => 'security',
                'is_emergency' => true,
            ],
            [
                'name' => 'Environmental',
                'slug' => 'environmental',
                'description' => 'Pollution, illegal dumping, environmental hazards',
                'color' => '#4CAF50',
                'icon' => 'eco',
                'is_emergency' => false,
            ],
            [
                'name' => 'Utilities',
                'slug' => 'utilities',
                'description' => 'Water, electricity, gas, telecommunications issues',
                'color' => '#FF9800',
                'icon' => 'build',
                'is_emergency' => false,
            ],
            [
                'name' => 'Emergency',
                'slug' => 'emergency',
                'description' => 'Fire, medical emergency, natural disasters',
                'color' => '#E91E63',
                'icon' => 'emergency',
                'is_emergency' => true,
            ],
        ];

        foreach ($categories as $categoryData) {
            IncidentCategory::firstOrCreate(
                ['name' => $categoryData['name']],
                $categoryData
            );
        }

        $this->command->info('Incident categories created successfully!');
    }
}
