<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('incident_updates', function (Blueprint $table) {
            $table->id();
            $table->foreignId('incident_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained(); // Staff member who made the update
            $table->enum('type', ['status_change', 'assignment', 'comment', 'media_added', 'system']);
            $table->text('message');
            $table->json('metadata')->nullable(); // Store additional data like old/new status
            $table->boolean('is_public')->default(true); // Whether visible to reporter
            $table->timestamps();
            
            $table->index(['incident_id', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('incident_updates');
    }
};
