import 'incident_category.dart';
import 'user.dart';
import 'incident_media.dart';
import 'incident_update.dart';

enum IncidentStatus { pending, acknowledged, inProgress, resolved, closed, cancelled }

enum IncidentPriority { low, medium, high, critical }

class Incident {
  final int id;
  final String incidentNumber;
  final int? userId;
  final int categoryId;
  final String title;
  final String description;
  final double latitude;
  final double longitude;
  final String? address;
  final String? landmark;
  final IncidentStatus status;
  final IncidentPriority priority;
  final bool isEmergency;
  final bool isAnonymous;
  final int? assignedDepartmentId;
  final int? assignedToUserId;
  final String? reporterName;
  final String? reporterPhone;
  final String? reporterEmail;
  final DateTime? acknowledgedAt;
  final DateTime? resolvedAt;
  final DateTime? closedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Relationships
  final IncidentCategory? category;
  final User? user;
  final User? assignedUser;
  final List<IncidentMedia>? media;
  final List<IncidentUpdate>? updates;

  Incident({
    required this.id,
    required this.incidentNumber,
    this.userId,
    required this.categoryId,
    required this.title,
    required this.description,
    required this.latitude,
    required this.longitude,
    this.address,
    this.landmark,
    required this.status,
    required this.priority,
    required this.isEmergency,
    required this.isAnonymous,
    this.assignedDepartmentId,
    this.assignedToUserId,
    this.reporterName,
    this.reporterPhone,
    this.reporterEmail,
    this.acknowledgedAt,
    this.resolvedAt,
    this.closedAt,
    required this.createdAt,
    required this.updatedAt,
    this.category,
    this.user,
    this.assignedUser,
    this.media,
    this.updates,
  });

  factory Incident.fromJson(Map<String, dynamic> json) {
    return Incident(
      id: json['id'],
      incidentNumber: json['incident_number'],
      userId: json['user_id'],
      categoryId: json['category_id'],
      title: json['title'],
      description: json['description'],
      latitude: double.parse(json['latitude'].toString()),
      longitude: double.parse(json['longitude'].toString()),
      address: json['address'],
      landmark: json['landmark'],
      status: _parseStatus(json['status']),
      priority: _parsePriority(json['priority']),
      isEmergency: json['is_emergency'],
      isAnonymous: json['is_anonymous'],
      assignedDepartmentId: json['assigned_department_id'],
      assignedToUserId: json['assigned_to_user_id'],
      reporterName: json['reporter_name'],
      reporterPhone: json['reporter_phone'],
      reporterEmail: json['reporter_email'],
      acknowledgedAt: json['acknowledged_at'] != null 
          ? DateTime.parse(json['acknowledged_at']) 
          : null,
      resolvedAt: json['resolved_at'] != null 
          ? DateTime.parse(json['resolved_at']) 
          : null,
      closedAt: json['closed_at'] != null 
          ? DateTime.parse(json['closed_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      category: json['category'] != null 
          ? IncidentCategory.fromJson(json['category']) 
          : null,
      user: json['user'] != null 
          ? User.fromJson(json['user']) 
          : null,
      assignedUser: json['assigned_user'] != null 
          ? User.fromJson(json['assigned_user']) 
          : null,
      media: json['media'] != null 
          ? (json['media'] as List).map((m) => IncidentMedia.fromJson(m)).toList()
          : null,
      updates: json['updates'] != null 
          ? (json['updates'] as List).map((u) => IncidentUpdate.fromJson(u)).toList()
          : null,
    );
  }

  static IncidentStatus _parseStatus(String status) {
    switch (status) {
      case 'pending': return IncidentStatus.pending;
      case 'acknowledged': return IncidentStatus.acknowledged;
      case 'in_progress': return IncidentStatus.inProgress;
      case 'resolved': return IncidentStatus.resolved;
      case 'closed': return IncidentStatus.closed;
      case 'cancelled': return IncidentStatus.cancelled;
      default: return IncidentStatus.pending;
    }
  }

  static IncidentPriority _parsePriority(String priority) {
    switch (priority) {
      case 'low': return IncidentPriority.low;
      case 'medium': return IncidentPriority.medium;
      case 'high': return IncidentPriority.high;
      case 'critical': return IncidentPriority.critical;
      default: return IncidentPriority.medium;
    }
  }

  String get statusString {
    switch (status) {
      case IncidentStatus.pending: return 'pending';
      case IncidentStatus.acknowledged: return 'acknowledged';
      case IncidentStatus.inProgress: return 'in_progress';
      case IncidentStatus.resolved: return 'resolved';
      case IncidentStatus.closed: return 'closed';
      case IncidentStatus.cancelled: return 'cancelled';
    }
  }

  String get priorityString {
    switch (priority) {
      case IncidentPriority.low: return 'low';
      case IncidentPriority.medium: return 'medium';
      case IncidentPriority.high: return 'high';
      case IncidentPriority.critical: return 'critical';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'incident_number': incidentNumber,
      'user_id': userId,
      'category_id': categoryId,
      'title': title,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'landmark': landmark,
      'status': statusString,
      'priority': priorityString,
      'is_emergency': isEmergency,
      'is_anonymous': isAnonymous,
      'assigned_department_id': assignedDepartmentId,
      'assigned_to_user_id': assignedToUserId,
      'reporter_name': reporterName,
      'reporter_phone': reporterPhone,
      'reporter_email': reporterEmail,
      'acknowledged_at': acknowledgedAt?.toIso8601String(),
      'resolved_at': resolvedAt?.toIso8601String(),
      'closed_at': closedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
