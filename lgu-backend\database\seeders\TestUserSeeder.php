<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    public function run(): void
    {
        // Create a test citizen user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Citizen',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'citizen',
                'status' => 'active',
                'phone' => '+63 ************',
                'address' => '123 Test Street, Test City',
                'email_verified_at' => now(),
            ]
        );

        // Create a test staff user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Staff',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'staff',
                'status' => 'active',
                'phone' => '+63 ************',
                'address' => 'LGU Office, Government Center',
                'employee_id' => 'EMP001',
                'position' => 'Emergency Response Officer',
                'email_verified_at' => now(),
            ]
        );

        // Create a test admin user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'user_type' => 'admin',
                'status' => 'active',
                'phone' => '+63 ************',
                'address' => 'LGU Office, Government Center',
                'employee_id' => 'ADM001',
                'position' => 'System Administrator',
                'email_verified_at' => now(),
            ]
        );

        echo "Test users created:\n";
        echo "Citizen: <EMAIL> / password123\n";
        echo "Staff: <EMAIL> / password123\n";
        echo "Admin: <EMAIL> / password123\n";
    }
}
