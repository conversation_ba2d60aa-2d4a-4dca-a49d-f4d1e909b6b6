<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SocialAuthController;
use App\Http\Controllers\Api\IncidentController;
use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\NotificationController;

// Public routes
Route::post('/login', [SocialAuthController::class, 'login']);
Route::post('/auth/google', [SocialAuthController::class, 'googleLogin']);

// Public incident routes (for anonymous reporting)
Route::post('/incidents', [IncidentController::class, 'store']);
Route::get('/incidents/{incident}', [IncidentController::class, 'show']);
Route::get('/categories', [CategoryController::class, 'index']);
Route::get('/categories/{category}', [CategoryController::class, 'show']);

// Device token registration (can be anonymous)
Route::post('/notifications/register-device', [NotificationController::class, 'registerDevice']);

// Protected routes
Route::middleware('auth:sanctum')->group(function () {
    // User routes
    Route::get('/user', function (Request $request) {
        return $request->user()->load('department');
    });
    Route::post('/logout', [SocialAuthController::class, 'logout']);

    // Incident routes
    Route::get('/incidents', [IncidentController::class, 'index']);
    Route::patch('/incidents/{incident}/status', [IncidentController::class, 'updateStatus']);

    // Notification routes
    Route::patch('/notifications/update-device', [NotificationController::class, 'updateDeviceToken']);
    Route::delete('/notifications/unregister-device', [NotificationController::class, 'unregisterDevice']);
});