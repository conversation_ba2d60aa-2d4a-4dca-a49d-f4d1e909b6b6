<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'user_type',
        'status',
        'google_id',
        'facebook_id',
        'avatar',
        'department_id',
        'employee_id',
        'position',
        'preferred_lat',
        'preferred_lng',
        'notification_radius',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'preferred_lat' => 'decimal:8',
            'preferred_lng' => 'decimal:8',
            'notification_radius' => 'integer',
        ];
    }

    // Relationships
    public function department(): BelongsTo
    {
        return $this->belongsTo(ResponderDepartment::class);
    }

    public function reportedIncidents(): HasMany
    {
        return $this->hasMany(Incident::class);
    }

    public function assignedIncidents(): HasMany
    {
        return $this->hasMany(Incident::class, 'assigned_to_user_id');
    }

    public function incidentUpdates(): HasMany
    {
        return $this->hasMany(IncidentUpdate::class);
    }

    public function deviceTokens(): HasMany
    {
        return $this->hasMany(UserDeviceToken::class);
    }

    // Scopes
    public function scopeCitizens($query)
    {
        return $query->where('user_type', 'citizen');
    }

    public function scopeStaff($query)
    {
        return $query->where('user_type', 'staff');
    }

    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    // Helper methods
    public function isCitizen(): bool
    {
        return $this->user_type === 'citizen';
    }

    public function isStaff(): bool
    {
        return in_array($this->user_type, ['staff', 'admin']);
    }

    public function isAdmin(): bool
    {
        return $this->user_type === 'admin';
    }
}
