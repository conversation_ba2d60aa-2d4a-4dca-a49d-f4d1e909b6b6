enum MediaType { image, video, audio, document }

class IncidentMedia {
  final int id;
  final int incidentId;
  final MediaType type;
  final String filePath;
  final String fileName;
  final String mimeType;
  final int fileSize;
  final String? description;
  final int sortOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  IncidentMedia({
    required this.id,
    required this.incidentId,
    required this.type,
    required this.filePath,
    required this.fileName,
    required this.mimeType,
    required this.fileSize,
    this.description,
    required this.sortOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory IncidentMedia.fromJson(Map<String, dynamic> json) {
    return IncidentMedia(
      id: json['id'],
      incidentId: json['incident_id'],
      type: _parseMediaType(json['type']),
      filePath: json['file_path'],
      fileName: json['file_name'],
      mimeType: json['mime_type'],
      fileSize: json['file_size'],
      description: json['description'],
      sortOrder: json['sort_order'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static MediaType _parseMediaType(String type) {
    switch (type) {
      case 'image': return MediaType.image;
      case 'video': return MediaType.video;
      case 'audio': return MediaType.audio;
      case 'document': return MediaType.document;
      default: return MediaType.image;
    }
  }

  String get typeString {
    switch (type) {
      case MediaType.image: return 'image';
      case MediaType.video: return 'video';
      case MediaType.audio: return 'audio';
      case MediaType.document: return 'document';
    }
  }

  String get fileSizeHuman {
    const units = ['B', 'KB', 'MB', 'GB'];
    double bytes = fileSize.toDouble();
    int unitIndex = 0;
    
    while (bytes >= 1024 && unitIndex < units.length - 1) {
      bytes /= 1024;
      unitIndex++;
    }
    
    return '${bytes.toStringAsFixed(2)} ${units[unitIndex]}';
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'incident_id': incidentId,
      'type': typeString,
      'file_path': filePath,
      'file_name': fileName,
      'mime_type': mimeType,
      'file_size': fileSize,
      'description': description,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
