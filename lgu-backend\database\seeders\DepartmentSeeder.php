<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Department;

class DepartmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Public Works Department',
                'description' => 'Handles infrastructure, road maintenance, and public facilities',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+63-2-123-4567',
            ],
            [
                'name' => 'Traffic Management Office',
                'description' => 'Manages traffic flow, traffic violations, and road safety',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+63-2-234-5678',
            ],
            [
                'name' => 'Environmental Services',
                'description' => 'Environmental protection, waste management, and pollution control',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+63-2-345-6789',
            ],
            [
                'name' => 'Public Safety Office',
                'description' => 'Public safety, emergency response, and security coordination',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+63-2-456-7890',
            ],
            [
                'name' => 'Utilities Department',
                'description' => 'Water, electricity, and telecommunications infrastructure',
                'contact_email' => '<EMAIL>',
                'contact_phone' => '+63-2-567-8901',
            ],
        ];

        foreach ($departments as $departmentData) {
            Department::firstOrCreate(
                ['name' => $departmentData['name']],
                $departmentData
            );
        }

        $this->command->info('Departments created successfully!');
    }
}
