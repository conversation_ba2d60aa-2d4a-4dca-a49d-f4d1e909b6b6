<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\IncidentCategory;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = IncidentCategory::active();

        if ($request->has('emergency')) {
            $query->where('is_emergency', $request->boolean('emergency'));
        }

        $categories = $query->orderBy('priority_level', 'desc')
                          ->orderBy('name')
                          ->get();

        return response()->json([
            'success' => true,
            'data' => $categories,
        ]);
    }

    public function show(IncidentCategory $category): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $category,
        ]);
    }
}
