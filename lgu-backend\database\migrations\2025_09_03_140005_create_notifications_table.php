<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->enum('type', ['incident_update', 'public_alert', 'system']);
            $table->string('title');
            $table->text('message');
            $table->json('data')->nullable(); // Additional data like incident_id
            
            // Target audience
            $table->enum('audience', ['all_users', 'specific_user', 'area_based', 'category_based']);
            $table->foreignId('target_user_id')->nullable()->constrained('users')->onDelete('cascade');
            $table->json('target_criteria')->nullable(); // For area/category based targeting
            
            // Scheduling
            $table->timestamp('scheduled_at')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->boolean('is_sent')->default(false);
            
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            
            $table->index(['audience', 'is_sent']);
            $table->index(['scheduled_at', 'is_sent']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
