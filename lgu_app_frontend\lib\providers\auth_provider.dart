import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';

enum AuthStatus {
  uninitialized,
  authenticated,
  unauthenticated,
  loading,
}

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  AuthStatus _status = AuthStatus.uninitialized;
  Map<String, dynamic>? _user;
  String? _token;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  Map<String, dynamic>? get user => _user;
  String? get token => _token;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;

  /// Initialize authentication state
  Future<void> initializeAuth() async {
    _setStatus(AuthStatus.loading);
    
    try {
      final isAuth = await _authService.isAuthenticated();
      
      if (isAuth) {
        // Validate token with server
        final isValid = await _authService.validateToken();
        
        if (isValid) {
          _user = await _authService.getUser();
          _token = await _authService.getToken();
          _setStatus(AuthStatus.authenticated);
        } else {
          // Token is invalid, clear auth data
          await _authService.clearAuthData();
          _setStatus(AuthStatus.unauthenticated);
        }
      } else {
        _setStatus(AuthStatus.unauthenticated);
      }
    } catch (e) {
      _setErrorMessage('Failed to initialize authentication: ${e.toString()}');
      _setStatus(AuthStatus.unauthenticated);
    }
  }

  /// Login with email and password
  Future<bool> login(String email, String password) async {
    _setStatus(AuthStatus.loading);
    _clearErrorMessage();

    try {
      final result = await _authService.login(email, password);
      
      if (result['success'] == true) {
        _user = result['user'];
        _token = result['token'];
        _setStatus(AuthStatus.authenticated);
        return true;
      } else {
        _setErrorMessage(result['message'] ?? 'Login failed');
        _setStatus(AuthStatus.unauthenticated);
        return false;
      }
    } catch (e) {
      _setErrorMessage('Login error: ${e.toString()}');
      _setStatus(AuthStatus.unauthenticated);
      return false;
    }
  }

  /// Logout user
  Future<void> logout() async {
    _setStatus(AuthStatus.loading);
    
    try {
      await _authService.logout();
    } catch (e) {
      // Log error but continue with logout
      debugPrint('Logout error: ${e.toString()}');
    } finally {
      // Clear local state regardless of API response
      _user = null;
      _token = null;
      _clearErrorMessage();
      _setStatus(AuthStatus.unauthenticated);
    }
  }

  /// Check and refresh authentication status
  Future<void> checkAuthStatus() async {
    if (_status == AuthStatus.loading) return;
    
    final isAuth = await _authService.isAuthenticated();
    
    if (!isAuth && _status == AuthStatus.authenticated) {
      // User was logged out externally
      _user = null;
      _token = null;
      _setStatus(AuthStatus.unauthenticated);
    } else if (isAuth && _status == AuthStatus.unauthenticated) {
      // User was logged in externally
      await initializeAuth();
    }
  }

  /// Get user data
  Future<Map<String, dynamic>?> getUserData() async {
    if (_user != null) return _user;
    
    try {
      _user = await _authService.getUser();
      return _user;
    } catch (e) {
      debugPrint('Error getting user data: ${e.toString()}');
      return null;
    }
  }

  /// Update user data locally
  void updateUser(Map<String, dynamic> userData) {
    _user = userData;
    notifyListeners();
  }

  /// Clear error message
  void clearError() {
    _clearErrorMessage();
  }

  // Private methods
  void _setStatus(AuthStatus status) {
    _status = status;
    notifyListeners();
  }

  void _setErrorMessage(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  void _clearErrorMessage() {
    _errorMessage = null;
    notifyListeners();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
