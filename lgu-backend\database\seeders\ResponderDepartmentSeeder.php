<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\ResponderDepartment;

class ResponderDepartmentSeeder extends Seeder
{
    public function run(): void
    {
        $departments = [
            [
                'name' => 'Police Department',
                'slug' => 'police',
                'description' => 'Law enforcement and public safety',
                'contact_number' => '911',
                'email' => '<EMAIL>',
                'address' => 'City Hall Complex, Main Street',
            ],
            [
                'name' => 'Fire Department',
                'slug' => 'fire',
                'description' => 'Fire suppression and emergency rescue',
                'contact_number' => '911',
                'email' => '<EMAIL>',
                'address' => 'Fire Station, Emergency Services Complex',
            ],
            [
                'name' => 'Medical Emergency Services',
                'slug' => 'medical',
                'description' => 'Emergency medical response and ambulance services',
                'contact_number' => '911',
                'email' => '<EMAIL>',
                'address' => 'City Hospital, Health District',
            ],
            [
                'name' => 'Traffic Management Office',
                'slug' => 'traffic',
                'description' => 'Traffic control and road safety',
                'contact_number' => '(02) 123-4567',
                'email' => '<EMAIL>',
                'address' => 'Traffic Management Center, City Hall',
            ],
            [
                'name' => 'Public Works Department',
                'slug' => 'public-works',
                'description' => 'Infrastructure maintenance and construction',
                'contact_number' => '(02) 234-5678',
                'email' => '<EMAIL>',
                'address' => 'Public Works Building, Government Center',
            ],
            [
                'name' => 'Utilities Department',
                'slug' => 'utilities',
                'description' => 'Water, power, and utility services',
                'contact_number' => '(02) 345-6789',
                'email' => '<EMAIL>',
                'address' => 'Utilities Office, Service Center',
            ],
            [
                'name' => 'Environmental Services',
                'slug' => 'environmental',
                'description' => 'Environmental protection and waste management',
                'contact_number' => '(02) 456-7890',
                'email' => '<EMAIL>',
                'address' => 'Environmental Office, City Hall Annex',
            ],
            [
                'name' => 'Disaster Risk Reduction Office',
                'slug' => 'disaster-management',
                'description' => 'Disaster preparedness and response coordination',
                'contact_number' => '(02) 567-8901',
                'email' => '<EMAIL>',
                'address' => 'DRRM Office, Emergency Operations Center',
            ],
        ];

        foreach ($departments as $department) {
            ResponderDepartment::create($department);
        }
    }
}
