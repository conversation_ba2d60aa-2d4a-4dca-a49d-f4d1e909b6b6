<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password')->nullable(); // Nullable for social login

            // Additional user fields for LGU system
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->enum('user_type', ['citizen', 'staff', 'admin'])->default('citizen');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');

            // Social login fields
            $table->string('google_id')->nullable();
            $table->string('facebook_id')->nullable();
            $table->string('avatar')->nullable();

            // Staff-specific fields
            $table->foreignId('department_id')->nullable()->constrained('responder_departments')->onDelete('set null');
            $table->string('employee_id')->nullable();
            $table->string('position')->nullable();

            // Location preferences for notifications
            $table->decimal('preferred_lat', 10, 8)->nullable();
            $table->decimal('preferred_lng', 11, 8)->nullable();
            $table->integer('notification_radius')->default(5000); // meters

            $table->rememberToken();
            $table->timestamps();

            $table->index(['user_type', 'status']);
            $table->index('google_id');
            $table->index('facebook_id');
        });

        Schema::create('password_reset_tokens', function (Blueprint $table) {
            $table->string('email')->primary();
            $table->string('token');
            $table->timestamp('created_at')->nullable();
        });

        Schema::create('sessions', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->foreignId('user_id')->nullable()->index();
            $table->string('ip_address', 45)->nullable();
            $table->text('user_agent')->nullable();
            $table->longText('payload');
            $table->integer('last_activity')->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
        Schema::dropIfExists('password_reset_tokens');
        Schema::dropIfExists('sessions');
    }
};
