<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('incidents', function (Blueprint $table) {
            $table->id();
            $table->string('incident_number')->unique(); // Auto-generated: INC-2025-000001
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // null for anonymous reports
            $table->foreignId('category_id')->constrained('incident_categories');
            $table->string('title');
            $table->text('description');
            
            // Location data
            $table->decimal('latitude', 10, 8);
            $table->decimal('longitude', 11, 8);
            $table->text('address')->nullable();
            $table->string('landmark')->nullable();
            
            // Status and priority
            $table->enum('status', ['pending', 'acknowledged', 'in_progress', 'resolved', 'closed', 'cancelled'])
                  ->default('pending');
            $table->enum('priority', ['low', 'medium', 'high', 'critical'])->default('medium');
            $table->boolean('is_emergency')->default(false);
            $table->boolean('is_anonymous')->default(false);
            
            // Assignment
            $table->foreignId('assigned_department_id')->nullable()->constrained('responder_departments')->onDelete('set null');
            $table->foreignId('assigned_to_user_id')->nullable()->constrained('users')->onDelete('set null');
            
            // Reporter contact (for anonymous reports)
            $table->string('reporter_name')->nullable();
            $table->string('reporter_phone')->nullable();
            $table->string('reporter_email')->nullable();
            
            // Timestamps
            $table->timestamp('acknowledged_at')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'created_at']);
            $table->index(['category_id', 'status']);
            $table->index(['latitude', 'longitude']);
            $table->index('incident_number');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('incidents');
    }
};
