<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Department extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'contact_email',
        'contact_phone',
    ];

    public function incidents()
    {
        return $this->hasMany(Incident::class, 'assigned_department_id');
    }

    public function users()
    {
        return $this->hasMany(User::class, 'department_id');
    }
}
