enum UserType { citizen, staff, admin }

enum UserStatus { active, inactive, suspended }

class User {
  final int id;
  final String name;
  final String email;
  final String? phone;
  final String? address;
  final UserType userType;
  final UserStatus status;
  final String? googleId;
  final String? facebookId;
  final String? avatar;
  final int? departmentId;
  final String? employeeId;
  final String? position;
  final double? preferredLat;
  final double? preferredLng;
  final int notificationRadius;
  final DateTime? emailVerifiedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    this.address,
    required this.userType,
    required this.status,
    this.googleId,
    this.facebookId,
    this.avatar,
    this.departmentId,
    this.employeeId,
    this.position,
    this.preferredLat,
    this.preferredLng,
    required this.notificationRadius,
    this.emailVerifiedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      address: json['address'],
      userType: _parseUserType(json['user_type']),
      status: _parseUserStatus(json['status']),
      googleId: json['google_id'],
      facebookId: json['facebook_id'],
      avatar: json['avatar'],
      departmentId: json['department_id'],
      employeeId: json['employee_id'],
      position: json['position'],
      preferredLat: json['preferred_lat'] != null 
          ? double.parse(json['preferred_lat'].toString()) 
          : null,
      preferredLng: json['preferred_lng'] != null 
          ? double.parse(json['preferred_lng'].toString()) 
          : null,
      notificationRadius: json['notification_radius'] ?? 5000,
      emailVerifiedAt: json['email_verified_at'] != null 
          ? DateTime.parse(json['email_verified_at']) 
          : null,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  static UserType _parseUserType(String type) {
    switch (type) {
      case 'citizen': return UserType.citizen;
      case 'staff': return UserType.staff;
      case 'admin': return UserType.admin;
      default: return UserType.citizen;
    }
  }

  static UserStatus _parseUserStatus(String status) {
    switch (status) {
      case 'active': return UserStatus.active;
      case 'inactive': return UserStatus.inactive;
      case 'suspended': return UserStatus.suspended;
      default: return UserStatus.active;
    }
  }

  String get userTypeString {
    switch (userType) {
      case UserType.citizen: return 'citizen';
      case UserType.staff: return 'staff';
      case UserType.admin: return 'admin';
    }
  }

  String get statusString {
    switch (status) {
      case UserStatus.active: return 'active';
      case UserStatus.inactive: return 'inactive';
      case UserStatus.suspended: return 'suspended';
    }
  }

  bool get isCitizen => userType == UserType.citizen;
  bool get isStaff => userType == UserType.staff || userType == UserType.admin;
  bool get isAdmin => userType == UserType.admin;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'address': address,
      'user_type': userTypeString,
      'status': statusString,
      'google_id': googleId,
      'facebook_id': facebookId,
      'avatar': avatar,
      'department_id': departmentId,
      'employee_id': employeeId,
      'position': position,
      'preferred_lat': preferredLat,
      'preferred_lng': preferredLng,
      'notification_radius': notificationRadius,
      'email_verified_at': emailVerifiedAt?.toIso8601String(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
