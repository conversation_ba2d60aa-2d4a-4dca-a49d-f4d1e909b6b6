import 'user.dart';

enum UpdateType { statusChange, assignment, comment, mediaAdded, system }

class IncidentUpdate {
  final int id;
  final int incidentId;
  final int userId;
  final UpdateType type;
  final String message;
  final Map<String, dynamic>? metadata;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  // Relationships
  final User? user;

  IncidentUpdate({
    required this.id,
    required this.incidentId,
    required this.userId,
    required this.type,
    required this.message,
    this.metadata,
    required this.isPublic,
    required this.createdAt,
    required this.updatedAt,
    this.user,
  });

  factory IncidentUpdate.fromJson(Map<String, dynamic> json) {
    return IncidentUpdate(
      id: json['id'],
      incidentId: json['incident_id'],
      userId: json['user_id'],
      type: _parseUpdateType(json['type']),
      message: json['message'],
      metadata: json['metadata'],
      isPublic: json['is_public'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      user: json['user'] != null ? User.fromJson(json['user']) : null,
    );
  }

  static UpdateType _parseUpdateType(String type) {
    switch (type) {
      case 'status_change': return UpdateType.statusChange;
      case 'assignment': return UpdateType.assignment;
      case 'comment': return UpdateType.comment;
      case 'media_added': return UpdateType.mediaAdded;
      case 'system': return UpdateType.system;
      default: return UpdateType.system;
    }
  }

  String get typeString {
    switch (type) {
      case UpdateType.statusChange: return 'status_change';
      case UpdateType.assignment: return 'assignment';
      case UpdateType.comment: return 'comment';
      case UpdateType.mediaAdded: return 'media_added';
      case UpdateType.system: return 'system';
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'incident_id': incidentId,
      'user_id': userId,
      'type': typeString,
      'message': message,
      'metadata': metadata,
      'is_public': isPublic,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
