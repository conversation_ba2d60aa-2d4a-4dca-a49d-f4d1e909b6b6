class IncidentCategory {
  final int id;
  final String name;
  final String slug;
  final String? description;
  final String? icon;
  final String color;
  final int priorityLevel;
  final bool isEmergency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  IncidentCategory({
    required this.id,
    required this.name,
    required this.slug,
    this.description,
    this.icon,
    required this.color,
    required this.priorityLevel,
    required this.isEmergency,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  factory IncidentCategory.fromJson(Map<String, dynamic> json) {
    return IncidentCategory(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      icon: json['icon'],
      color: json['color'],
      priorityLevel: json['priority_level'],
      isEmergency: json['is_emergency'],
      isActive: json['is_active'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'icon': icon,
      'color': color,
      'priority_level': priorityLevel,
      'is_emergency': isEmergency,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }
}
