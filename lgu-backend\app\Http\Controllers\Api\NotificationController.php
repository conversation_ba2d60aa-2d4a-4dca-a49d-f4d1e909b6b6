<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserDeviceToken;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class NotificationController extends Controller
{
    public function registerDevice(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_token' => 'required|string',
            'platform' => 'required|in:android,ios,web',
            'device_id' => 'nullable|string',
            'device_info' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        // Remove existing token if it exists
        UserDeviceToken::where('device_token', $request->device_token)->delete();

        // Create new token record
        $deviceToken = UserDeviceToken::create([
            'user_id' => $request->user()?->id,
            'device_token' => $request->device_token,
            'platform' => $request->platform,
            'device_id' => $request->device_id,
            'device_info' => $request->device_info,
            'last_used_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Device token registered successfully',
            'data' => $deviceToken,
        ]);
    }

    public function updateDeviceToken(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'old_token' => 'required|string',
            'new_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $deviceToken = UserDeviceToken::where('device_token', $request->old_token)->first();

        if (!$deviceToken) {
            return response()->json([
                'success' => false,
                'message' => 'Device token not found',
            ], 404);
        }

        $deviceToken->update([
            'device_token' => $request->new_token,
            'last_used_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Device token updated successfully',
        ]);
    }

    public function unregisterDevice(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'device_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        UserDeviceToken::where('device_token', $request->device_token)->delete();

        return response()->json([
            'success' => true,
            'message' => 'Device token unregistered successfully',
        ]);
    }
}
