import 'dart:io';
import 'package:dio/dio.dart';
import '../models/incident.dart';
import '../models/incident_category.dart';
import 'api_service.dart';

class IncidentService {
  final ApiService _apiService = ApiService();

  // Get incident categories
  Future<List<IncidentCategory>> getCategories({bool? emergency}) async {
    try {
      final response = await _apiService.get('/categories', queryParameters: {
        if (emergency != null) 'emergency': emergency,
      });

      if (response.data['success']) {
        final List<dynamic> categoriesJson = response.data['data'];
        return categoriesJson.map((json) => IncidentCategory.fromJson(json)).toList();
      } else {
        throw Exception('Failed to load categories');
      }
    } on DioException catch (e) {
      throw Exception(_apiService.getErrorMessage(e));
    }
  }

  // Get incidents with filters
  Future<Map<String, dynamic>> getIncidents({
    int page = 1,
    int perPage = 15,
    String? status,
    int? categoryId,
    String? priority,
    bool? emergency,
    double? lat,
    double? lng,
    int? radius,
    bool? myReports,
  }) async {
    try {
      final response = await _apiService.get('/incidents', queryParameters: {
        'page': page,
        'per_page': perPage,
        if (status != null) 'status': status,
        if (categoryId != null) 'category_id': categoryId,
        if (priority != null) 'priority': priority,
        if (emergency != null) 'emergency': emergency,
        if (lat != null) 'lat': lat,
        if (lng != null) 'lng': lng,
        if (radius != null) 'radius': radius,
        if (myReports == true) 'my_reports': true,
      });

      if (response.data['success']) {
        final data = response.data['data'];
        final List<dynamic> incidentsJson = data['data'];
        final incidents = incidentsJson.map((json) => Incident.fromJson(json)).toList();
        
        return {
          'incidents': incidents,
          'pagination': {
            'current_page': data['current_page'],
            'last_page': data['last_page'],
            'per_page': data['per_page'],
            'total': data['total'],
          },
        };
      } else {
        throw Exception('Failed to load incidents');
      }
    } on DioException catch (e) {
      throw Exception(_apiService.getErrorMessage(e));
    }
  }

  // Get single incident
  Future<Incident> getIncident(int id) async {
    try {
      final response = await _apiService.get('/incidents/$id');

      if (response.data['success']) {
        return Incident.fromJson(response.data['data']);
      } else {
        throw Exception('Failed to load incident');
      }
    } on DioException catch (e) {
      throw Exception(_apiService.getErrorMessage(e));
    }
  }

  // Submit incident report
  Future<Incident> submitIncident({
    required int categoryId,
    required String title,
    required String description,
    required double latitude,
    required double longitude,
    String? address,
    String? landmark,
    bool isAnonymous = false,
    String? reporterName,
    String? reporterPhone,
    String? reporterEmail,
    List<File>? mediaFiles,
  }) async {
    try {
      Map<String, dynamic> data = {
        'category_id': categoryId,
        'title': title,
        'description': description,
        'latitude': latitude,
        'longitude': longitude,
        if (address != null) 'address': address,
        if (landmark != null) 'landmark': landmark,
        'is_anonymous': isAnonymous,
        if (reporterName != null) 'reporter_name': reporterName,
        if (reporterPhone != null) 'reporter_phone': reporterPhone,
        if (reporterEmail != null) 'reporter_email': reporterEmail,
      };

      Response response;
      
      if (mediaFiles != null && mediaFiles.isNotEmpty) {
        response = await _apiService.uploadFiles('/incidents', mediaFiles, data: data);
      } else {
        response = await _apiService.post('/incidents', data: data);
      }

      if (response.data['success']) {
        return Incident.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Failed to submit incident');
      }
    } on DioException catch (e) {
      throw Exception(_apiService.getErrorMessage(e));
    }
  }

  // Update incident status (for staff)
  Future<Incident> updateIncidentStatus({
    required int incidentId,
    required String status,
    String? message,
    int? assignedDepartmentId,
    int? assignedToUserId,
  }) async {
    try {
      final response = await _apiService.patch('/incidents/$incidentId/status', data: {
        'status': status,
        if (message != null) 'message': message,
        if (assignedDepartmentId != null) 'assigned_department_id': assignedDepartmentId,
        if (assignedToUserId != null) 'assigned_to_user_id': assignedToUserId,
      });

      if (response.data['success']) {
        return Incident.fromJson(response.data['data']);
      } else {
        throw Exception(response.data['message'] ?? 'Failed to update incident');
      }
    } on DioException catch (e) {
      throw Exception(_apiService.getErrorMessage(e));
    }
  }
}
