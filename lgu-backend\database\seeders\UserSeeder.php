<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = [
            [
                'name' => 'Citizen User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'citizen',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Staff Member',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'staff',
                'email_verified_at' => now(),
            ],
            [
                'name' => 'System Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role' => 'admin',
                'email_verified_at' => now(),
            ],
        ];

        foreach ($users as $userData) {
            User::firstOrCreate(
                ['email' => $userData['email']],
                $userData
            );
        }

        $this->command->info('Test users created successfully!');
        $this->command->info('Citizen: <EMAIL> / password123');
        $this->command->info('Staff: <EMAIL> / password123');
        $this->command->info('Admin: <EMAIL> / password123');
    }
}
