<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Run all seeders
        $this->call([
            UserSeeder::class,
            CategorySeeder::class,
            DepartmentSeeder::class,
            IncidentSeeder::class,
        ]);

        // Create additional test users for login testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
                'role' => 'citizen',
            ]
        );

        // Create an admin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'LGU Admin',
                'password' => Hash::make('admin123'),
                'email_verified_at' => now(),
                'role' => 'admin',
            ]
        );

        $this->command->info('All seeders completed successfully!');
        $this->command->info('Test User: <EMAIL> / password123');
        $this->command->info('Admin User: <EMAIL> / admin123');
    }
}
