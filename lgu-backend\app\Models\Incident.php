<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Incident extends Model
{
    use HasFactory;

    protected $fillable = [
        'incident_number',
        'user_id',
        'category_id',
        'title',
        'description',
        'latitude',
        'longitude',
        'address',
        'landmark',
        'status',
        'priority',
        'is_emergency',
        'is_anonymous',
        'assigned_department_id',
        'assigned_to_user_id',
        'reporter_name',
        'reporter_phone',
        'reporter_email',
        'acknowledged_at',
        'resolved_at',
        'closed_at',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_emergency' => 'boolean',
        'is_anonymous' => 'boolean',
        'acknowledged_at' => 'datetime',
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($incident) {
            if (empty($incident->incident_number)) {
                $incident->incident_number = self::generateIncidentNumber();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(IncidentCategory::class);
    }

    public function assignedDepartment(): BelongsTo
    {
        return $this->belongsTo(ResponderDepartment::class, 'assigned_department_id');
    }

    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }

    public function media(): HasMany
    {
        return $this->hasMany(IncidentMedia::class);
    }

    public function updates(): HasMany
    {
        return $this->hasMany(IncidentUpdate::class)->orderBy('created_at', 'desc');
    }

    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeEmergency($query)
    {
        return $query->where('is_emergency', true);
    }

    public function scopeNearLocation($query, $lat, $lng, $radius = 5000)
    {
        return $query->selectRaw("
            *, (
                6371000 * acos(
                    cos(radians(?)) * cos(radians(latitude)) * 
                    cos(radians(longitude) - radians(?)) + 
                    sin(radians(?)) * sin(radians(latitude))
                )
            ) AS distance
        ", [$lat, $lng, $lat])
        ->having('distance', '<=', $radius)
        ->orderBy('distance');
    }

    public static function generateIncidentNumber(): string
    {
        $year = date('Y');
        $lastIncident = self::whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();
        
        $nextNumber = $lastIncident ? 
            intval(substr($lastIncident->incident_number, -6)) + 1 : 1;
        
        return sprintf('INC-%s-%06d', $year, $nextNumber);
    }

    public function getReporterNameAttribute($value)
    {
        if ($this->is_anonymous && !$value) {
            return 'Anonymous Reporter';
        }
        return $value ?: $this->user?->name;
    }
}
