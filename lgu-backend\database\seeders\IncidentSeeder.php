<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Incident;
use App\Models\IncidentCategory;
use App\Models\User;

class IncidentSeeder extends Seeder
{
    public function run()
    {
        $categories = IncidentCategory::all();
        $users = User::all();

        if ($categories->isEmpty() || $users->isEmpty()) {
            $this->command->warn('Please run CategorySeeder and UserSeeder first');
            return;
        }

        $incidents = [
            [
                'title' => 'Broken Street Light on Main Street',
                'description' => 'The street light near the intersection of Main Street and 1st Avenue has been broken for several days. This creates a safety hazard for pedestrians and drivers at night.',
                'latitude' => 14.5995,
                'longitude' => 120.9842,
                'address' => 'Main Street, Manila, Philippines',
                'landmark' => 'Near Manila City Hall',
                'status' => 'pending',
                'priority' => 'medium',
                'is_emergency' => false,
                'is_anonymous' => false,
            ],
            [
                'title' => 'Pothole on Highway 101',
                'description' => 'Large pothole causing damage to vehicles. Multiple cars have reported tire damage from this location.',
                'latitude' => 14.6042,
                'longitude' => 120.9822,
                'address' => 'Highway 101, Manila, Philippines',
                'landmark' => 'Near SM Mall',
                'status' => 'in_progress',
                'priority' => 'high',
                'is_emergency' => false,
                'is_anonymous' => false,
            ],
            [
                'title' => 'Water Leak Emergency',
                'description' => 'Major water pipe burst causing flooding in residential area. Immediate attention required.',
                'latitude' => 14.5889,
                'longitude' => 120.9750,
                'address' => 'Residential Area, Barangay 123, Manila',
                'landmark' => 'Near Elementary School',
                'status' => 'resolved',
                'priority' => 'critical',
                'is_emergency' => true,
                'is_anonymous' => false,
            ],
            [
                'title' => 'Illegal Dumping Site',
                'description' => 'People are illegally dumping garbage in this vacant lot. It\'s becoming a health hazard and attracting pests.',
                'latitude' => 14.6100,
                'longitude' => 120.9900,
                'address' => 'Vacant Lot, Barangay 456, Manila',
                'landmark' => 'Behind the Market',
                'status' => 'pending',
                'priority' => 'medium',
                'is_emergency' => false,
                'is_anonymous' => true,
                'reporter_name' => 'Concerned Citizen',
            ],
            [
                'title' => 'Traffic Light Malfunction',
                'description' => 'Traffic light at busy intersection is stuck on red in all directions, causing major traffic congestion.',
                'latitude' => 14.5950,
                'longitude' => 120.9800,
                'address' => 'Intersection of Rizal Ave and Quezon Blvd',
                'landmark' => 'Near Central Bank',
                'status' => 'in_progress',
                'priority' => 'high',
                'is_emergency' => false,
                'is_anonymous' => false,
            ],
            [
                'title' => 'Stray Dogs in Park',
                'description' => 'Pack of stray dogs in the public park. Some appear aggressive and are scaring children and families.',
                'latitude' => 14.5850,
                'longitude' => 120.9700,
                'address' => 'Central Park, Manila',
                'landmark' => 'Near the playground',
                'status' => 'pending',
                'priority' => 'medium',
                'is_emergency' => false,
                'is_anonymous' => false,
            ],
        ];

        foreach ($incidents as $incidentData) {
            $incident = new Incident();
            $incident->incident_number = 'INC-' . date('Y') . '-' . str_pad(Incident::count() + 1, 6, '0', STR_PAD_LEFT);
            $incident->user_id = $users->random()->id;
            $incident->category_id = $categories->random()->id;
            $incident->title = $incidentData['title'];
            $incident->description = $incidentData['description'];
            $incident->latitude = $incidentData['latitude'];
            $incident->longitude = $incidentData['longitude'];
            $incident->address = $incidentData['address'];
            $incident->landmark = $incidentData['landmark'];
            $incident->status = $incidentData['status'];
            $incident->priority = $incidentData['priority'];
            $incident->is_emergency = $incidentData['is_emergency'];
            $incident->is_anonymous = $incidentData['is_anonymous'];
            
            if (isset($incidentData['reporter_name'])) {
                $incident->reporter_name = $incidentData['reporter_name'];
            }
            
            $incident->save();
        }

        $this->command->info('Sample incidents created successfully!');
    }
}
