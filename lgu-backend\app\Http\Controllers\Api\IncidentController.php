<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Incident;
use App\Models\IncidentCategory;
use App\Models\IncidentMedia;
use App\Models\IncidentUpdate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class IncidentController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = Incident::with(['category', 'user', 'assignedDepartment', 'media'])
            ->orderBy('created_at', 'desc');

        // Filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('category_id')) {
            $query->where('category_id', $request->category_id);
        }

        if ($request->has('priority')) {
            $query->where('priority', $request->priority);
        }

        if ($request->has('emergency')) {
            $query->where('is_emergency', $request->boolean('emergency'));
        }

        // Location-based filtering
        if ($request->has(['lat', 'lng', 'radius'])) {
            $query->nearLocation(
                $request->lat,
                $request->lng,
                $request->radius ?? 5000
            );
        }

        // User's own incidents (for citizens)
        if ($request->has('my_reports') && $request->user()) {
            $query->where('user_id', $request->user()->id);
        }

        $incidents = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $incidents,
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'category_id' => 'required|exists:incident_categories,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'address' => 'nullable|string',
            'landmark' => 'nullable|string',
            'is_anonymous' => 'boolean',
            'reporter_name' => 'required_if:is_anonymous,true|string|max:255',
            'reporter_phone' => 'nullable|string|max:20',
            'reporter_email' => 'nullable|email',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,mp4,mov,avi|max:10240', // 10MB max
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $category = IncidentCategory::find($request->category_id);
        
        $incident = Incident::create([
            'user_id' => $request->boolean('is_anonymous') ? null : $request->user()?->id,
            'category_id' => $request->category_id,
            'title' => $request->title,
            'description' => $request->description,
            'latitude' => $request->latitude,
            'longitude' => $request->longitude,
            'address' => $request->address,
            'landmark' => $request->landmark,
            'priority' => $this->determinePriority($category, $request),
            'is_emergency' => $category->is_emergency,
            'is_anonymous' => $request->boolean('is_anonymous'),
            'reporter_name' => $request->reporter_name,
            'reporter_phone' => $request->reporter_phone,
            'reporter_email' => $request->reporter_email,
        ]);

        // Handle media uploads
        if ($request->hasFile('media')) {
            $this->handleMediaUploads($incident, $request->file('media'));
        }

        // Create initial update
        IncidentUpdate::create([
            'incident_id' => $incident->id,
            'user_id' => $request->user()?->id ?? 1, // System user for anonymous
            'type' => 'system',
            'message' => 'Incident report submitted',
            'is_public' => true,
        ]);

        $incident->load(['category', 'media']);

        return response()->json([
            'success' => true,
            'message' => 'Incident reported successfully',
            'data' => $incident,
        ], 201);
    }

    public function show(Incident $incident): JsonResponse
    {
        $incident->load([
            'category',
            'user',
            'assignedDepartment',
            'assignedUser',
            'media',
            'updates.user'
        ]);

        return response()->json([
            'success' => true,
            'data' => $incident,
        ]);
    }

    public function updateStatus(Request $request, Incident $incident): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:pending,acknowledged,in_progress,resolved,closed,cancelled',
            'message' => 'nullable|string',
            'assigned_department_id' => 'nullable|exists:responder_departments,id',
            'assigned_to_user_id' => 'nullable|exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $oldStatus = $incident->status;
        $incident->update([
            'status' => $request->status,
            'assigned_department_id' => $request->assigned_department_id,
            'assigned_to_user_id' => $request->assigned_to_user_id,
            'acknowledged_at' => $request->status === 'acknowledged' ? now() : $incident->acknowledged_at,
            'resolved_at' => $request->status === 'resolved' ? now() : $incident->resolved_at,
            'closed_at' => $request->status === 'closed' ? now() : $incident->closed_at,
        ]);

        // Create update record
        IncidentUpdate::create([
            'incident_id' => $incident->id,
            'user_id' => $request->user()->id,
            'type' => 'status_change',
            'message' => $request->message ?? "Status changed from {$oldStatus} to {$request->status}",
            'metadata' => [
                'old_status' => $oldStatus,
                'new_status' => $request->status,
            ],
            'is_public' => true,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Incident status updated successfully',
            'data' => $incident->fresh(['category', 'assignedDepartment', 'assignedUser']),
        ]);
    }

    private function determinePriority(IncidentCategory $category, Request $request): string
    {
        if ($category->is_emergency) {
            return 'critical';
        }

        return match ($category->priority_level) {
            4 => 'critical',
            3 => 'high',
            2 => 'medium',
            default => 'low',
        };
    }

    private function handleMediaUploads(Incident $incident, array $files): void
    {
        foreach ($files as $index => $file) {
            $filename = Str::uuid() . '.' . $file->getClientOriginalExtension();
            $path = $file->storeAs('incidents/' . $incident->id, $filename, 'public');

            $type = str_starts_with($file->getMimeType(), 'image/') ? 'image' : 
                   (str_starts_with($file->getMimeType(), 'video/') ? 'video' : 'document');

            IncidentMedia::create([
                'incident_id' => $incident->id,
                'type' => $type,
                'file_path' => $path,
                'file_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getMimeType(),
                'file_size' => $file->getSize(),
                'sort_order' => $index,
            ]);
        }
    }
}
